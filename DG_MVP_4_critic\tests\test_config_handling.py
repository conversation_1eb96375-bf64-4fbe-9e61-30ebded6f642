"""
Test configuration handling for GeoTIFF creation and plotting modules.

This module tests the fixes for configuration parameter type mismatches
that were causing failures in Steps 4 and 5 of the HSI pipeline.

Following TDD methodology from docs/TDD.md with Given-When-Then format.
"""

import pytest
import tempfile
import os
from unittest.mock import patch, MagicMock
from pathlib import Path

from src.hsi_pipeline.create_georeferenced_rgb import run_create_rgb_geotiff
from src.hsi_pipeline.plotting.plot_hsi_data import run_plotting
from src.hsi_pipeline.pipeline_exceptions import PipelineConfigError


class TestGeoTIFFConfigHandling:
    """Test configuration handling for GeoTIFF creation module."""

    def test_geotiff_creation_accepts_config_dict(self):
        """
        Given a valid configuration dictionary
        When calling run_create_rgb_geotiff with dict parameter
        Then should process without configuration loading errors
        """
        # Arrange
        config_dict = {
            'paths': {
                'hsi_data_directory': 'test_hsi_dir',
                'hsi_base_filename': 'test_hsi',
                'output_directory': 'test_output',
                'georeferenced_pixels_csv': 'test_pixels.csv',
                'georeferenced_rgb_tif': 'test_output.tif'
            },
            'parameters': {
                'rgb_geotiff_creation': {
                    'target_wavelength_R_nm': 650,
                    'target_wavelength_G_nm': 550,
                    'target_wavelength_B_nm': 450,
                    'target_resolution_meters': 1.0,
                    'output_epsg_code': 4326,
                    'normalization_method': 'min_max'
                }
            }
        }

        # Mock all the file operations and external dependencies
        with patch('src.hsi_pipeline.create_georeferenced_rgb.spectral.open_image') as mock_spectral, \
             patch('src.hsi_pipeline.create_georeferenced_rgb.pd.read_csv') as mock_read_csv, \
             patch('src.hsi_pipeline.create_georeferenced_rgb.os.makedirs'), \
             patch('src.hsi_pipeline.create_georeferenced_rgb.rasterio.open'):
            
            # Configure mocks to simulate successful processing
            mock_img = MagicMock()
            mock_img.shape = (100, 200, 50)  # lines, samples, bands
            mock_img.bands.centers = [400 + i*10 for i in range(50)]  # wavelengths
            mock_img.load.return_value = MagicMock()
            mock_spectral.return_value = mock_img
            
            # Mock georeferencing data
            mock_df = MagicMock()
            mock_df.empty = False
            mock_df.__getitem__.return_value.min.return_value = 0
            mock_df.__getitem__.return_value.max.return_value = 100
            mock_read_csv.return_value = mock_df

            # Act & Assert - Should not raise PipelineConfigError for config type
            try:
                run_create_rgb_geotiff(config_dict)
            except PipelineConfigError as e:
                # Should not fail due to config type issues
                assert "Invalid config parameter type" not in str(e)

    def test_geotiff_creation_accepts_config_path(self):
        """
        Given a valid configuration file path
        When calling run_create_rgb_geotiff with string parameter
        Then should load configuration and process successfully
        """
        # Arrange
        config_content = """
[paths]
hsi_data_directory = "test_hsi_dir"
hsi_base_filename = "test_hsi"
output_directory = "test_output"
georeferenced_pixels_csv = "test_pixels.csv"
georeferenced_rgb_tif = "test_output.tif"

[parameters.rgb_geotiff_creation]
target_wavelength_R_nm = 650
target_wavelength_G_nm = 550
target_wavelength_B_nm = 450
target_resolution_meters = 1.0
output_epsg_code = 4326
normalization_method = "min_max"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(config_content)
            config_path = f.name

        try:
            # Mock external dependencies
            with patch('src.hsi_pipeline.create_georeferenced_rgb.spectral.open_image') as mock_spectral, \
                 patch('src.hsi_pipeline.create_georeferenced_rgb.pd.read_csv') as mock_read_csv, \
                 patch('src.hsi_pipeline.create_georeferenced_rgb.os.makedirs'), \
                 patch('src.hsi_pipeline.create_georeferenced_rgb.rasterio.open'):
                
                # Configure mocks
                mock_img = MagicMock()
                mock_img.shape = (100, 200, 50)
                mock_img.bands.centers = [400 + i*10 for i in range(50)]
                mock_img.load.return_value = MagicMock()
                mock_spectral.return_value = mock_img
                
                mock_df = MagicMock()
                mock_df.empty = False
                mock_df.__getitem__.return_value.min.return_value = 0
                mock_df.__getitem__.return_value.max.return_value = 100
                mock_read_csv.return_value = mock_df

                # Act & Assert - Should not raise configuration loading errors
                try:
                    run_create_rgb_geotiff(config_path)
                except PipelineConfigError as e:
                    assert "Configuration file not found" not in str(e)
                    assert "Error loading configuration file" not in str(e)

        finally:
            os.unlink(config_path)

    def test_geotiff_creation_invalid_config_type_raises_exception(self):
        """
        Given an invalid configuration parameter type
        When calling run_create_rgb_geotiff
        Then should raise PipelineConfigError with descriptive message
        """
        # Arrange
        invalid_config = 12345  # Neither dict nor string

        # Act & Assert
        with pytest.raises(PipelineConfigError) as exc_info:
            run_create_rgb_geotiff(invalid_config)
        
        assert "Invalid config parameter type" in str(exc_info.value)


class TestPlottingConfigHandling:
    """Test configuration handling for plotting module."""

    def test_plotting_accepts_config_dict(self):
        """
        Given a valid configuration dictionary
        When calling run_plotting with dict parameter
        Then should process without configuration loading errors
        """
        # Arrange
        config_dict = {
            'paths': {
                'output_directory': 'test_output',
                'hsi_poses_csv': 'test_poses.csv',
                'plot_output_directory': 'test_plots'
            }
        }

        # Mock file operations
        with patch('src.hsi_pipeline.plotting.plot_hsi_data.pd.read_csv') as mock_read_csv, \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.os.makedirs'), \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_positions'), \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_trajectory_2d'), \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_orientations'), \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_interpolation_quality'), \
             patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_yaw_and_flight_direction'):
            
            # Configure mock data
            mock_df = MagicMock()
            mock_read_csv.return_value = mock_df

            # Act & Assert - Should not raise PipelineConfigError for config type
            try:
                result = run_plotting(config_dict)
                assert result is True
            except PipelineConfigError as e:
                assert "Invalid config parameter type" not in str(e)

    def test_plotting_accepts_config_path(self):
        """
        Given a valid configuration file path
        When calling run_plotting with string parameter
        Then should load configuration and process successfully
        """
        # Arrange
        config_content = """
[paths]
output_directory = "test_output"
hsi_poses_csv = "test_poses.csv"
plot_output_directory = "test_plots"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(config_content)
            config_path = f.name

        try:
            # Mock file operations
            with patch('src.hsi_pipeline.plotting.plot_hsi_data.pd.read_csv') as mock_read_csv, \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.os.makedirs'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_positions'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_trajectory_2d'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_orientations'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_interpolation_quality'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_yaw_and_flight_direction'):
                
                mock_df = MagicMock()
                mock_read_csv.return_value = mock_df

                # Act & Assert
                try:
                    result = run_plotting(config_path)
                    assert result is True
                except PipelineConfigError as e:
                    assert "Configuration file not found" not in str(e)
                    assert "Error loading configuration file" not in str(e)

        finally:
            os.unlink(config_path)

    def test_plotting_invalid_config_type_raises_exception(self):
        """
        Given an invalid configuration parameter type
        When calling run_plotting
        Then should raise PipelineConfigError with descriptive message
        """
        # Arrange
        invalid_config = ['not', 'a', 'valid', 'config']  # Neither dict nor string

        # Act & Assert
        with pytest.raises(PipelineConfigError) as exc_info:
            run_plotting(invalid_config)
        
        assert "Invalid config parameter type" in str(exc_info.value)


class TestBackwardCompatibility:
    """Test that existing usage patterns continue to work."""

    def test_standalone_execution_still_works(self):
        """
        Given modules are executed standalone with file paths
        When using the traditional config_path parameter
        Then should continue working without breaking changes
        """
        # This test verifies that the parameter name change doesn't break existing code
        # The functions now accept config_or_path but should work the same way
        
        config_content = """
[paths]
output_directory = "test_output"
hsi_poses_csv = "test_poses.csv"
plot_output_directory = "test_plots"
"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.toml', delete=False) as f:
            f.write(config_content)
            config_path = f.name

        try:
            with patch('src.hsi_pipeline.plotting.plot_hsi_data.pd.read_csv'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.os.makedirs'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_positions'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_trajectory_2d'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_orientations'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_interpolation_quality'), \
                 patch('src.hsi_pipeline.plotting.plot_hsi_data.plot_yaw_and_flight_direction'):
                
                # Act - Should work with positional argument (backward compatibility)
                result = run_plotting(config_path)
                assert result is True

        finally:
            os.unlink(config_path)
