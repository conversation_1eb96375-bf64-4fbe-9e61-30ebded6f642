"""
Main pipeline script for HSI Georeferencing.

This module orchestrates the entire HSI direct georeferencing workflow,
including configuration loading, pose data consolidation, synchronization,
pixel georeferencing, and optional output generation (e.g., RGB GeoTIFFs, plots).
It serves as the main entry point for running the complete pipeline.

The pipeline consists of five main steps:
1. WebODM Pose Consolidation - Process and consolidate pose data from WebODM
2. HSI Pose Synchronization - Synchronize HSI data with pose information
3. Direct HSI Pixel Georeferencing - Perform the core georeferencing calculations
4. Georeferenced RGB GeoTIFF Creation - Generate RGB output products (optional)
5. Plot Generation - Create visualization plots (optional)

Author: HSI Georeferencing Pipeline Team
Version: 2.0 (LS5 Enhanced)
"""

import toml
import logging
import datetime
from pathlib import Path
from typing import Dict, Any, List

from .create_consolidated_webodm_poses import run_consolidation
from .synchronize_hsi_webodm import run_synchronization
from .georeference_hsi_pixels import run_georeferencing
from .create_georeferenced_rgb import run_create_rgb_geotiff
from .plotting.plot_hsi_data import run_plotting
from .logging_config import setup_logging, get_logger
from .pipeline_exceptions import (
    PipelineError, PipelineConfigError, HSIDataError,
    SynchronizationError, GeoreferencingError
)
from .batch_processing import (
    BatchFileManager, BatchProgressTracker, BatchMemoryManager,
    BatchResult, BatchSummary, merge_dataset_config, validate_batch_config,
    save_batch_summary
)

# LS7_7: Module-level logger initialization
logger = get_logger(__name__)

def load_pipeline_config(config_path: str) -> Dict[str, Any]:
    """
    Load and validate the pipeline configuration file.

    Args:
        config_path: Path to the TOML configuration file

    Returns:
        Loaded configuration dictionary

    Raises:
        PipelineConfigError: If config file cannot be loaded or is invalid
    """
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            raise PipelineConfigError(f"Configuration file not found: {config_path}")

        with open(config_file, 'r', encoding='utf-8') as f:
            config = toml.load(f)

        logger.info(f"Successfully loaded configuration from: {config_path}")
        return config

    except toml.TomlDecodeError as e:
        raise PipelineConfigError(f"Invalid TOML syntax in config file: {e}") from e
    except Exception as e:
        raise PipelineConfigError(f"Error loading configuration file: {e}") from e


def run_single_dataset_pipeline(config: Dict[str, Any], dataset_name: str = None) -> bool:
    """
    Execute the pipeline for a single dataset.

    Args:
        config: Configuration dictionary
        dataset_name: Optional dataset name for batch processing

    Returns:
        True if the pipeline completed successfully, False otherwise.
    """
    logger.info(f"Processing dataset: {dataset_name or 'single dataset'}")
    pipeline_successful = True

    try:
        # Step 1: WebODM Pose Consolidation
        logger.info("--- Step 1: WebODM Pose Consolidation ---")
        try:
            if not run_consolidation(config):
                raise PipelineError("WebODM pose consolidation failed")
            logger.info("Step 1 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 1: WebODM Pose Consolidation. {e}")
            return False

        # Step 2: HSI Pose Synchronization
        logger.info("--- Step 2: HSI Pose Synchronization ---")
        try:
            if not run_synchronization(config):
                raise SynchronizationError("HSI pose synchronization failed")
            logger.info("Step 2 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 2: HSI Pose Synchronization. {e}")
            return False

        # Step 3: Direct HSI Pixel Georeferencing
        logger.info("--- Step 3: Direct HSI Pixel Georeferencing ---")
        try:
            if not run_georeferencing(config):
                raise GeoreferencingError("HSI pixel georeferencing failed")
            logger.info("Step 3 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 3: Direct HSI Pixel Georeferencing. {e}")
            return False

        # Step 4: Georeferenced RGB GeoTIFF Creation (optional)
        logger.info("--- Step 4: Georeferenced RGB GeoTIFF Creation ---")
        try:
            if not run_create_rgb_geotiff(config, dataset_name):
                logger.warning("Step 4: Error creating georeferenced RGB GeoTIFF.")
                pipeline_successful = False  # Mark as not fully successful
            else:
                logger.info("Step 4 completed successfully.")
        except Exception as e:
            logger.warning(f"Step 4: Error creating georeferenced RGB GeoTIFF: {e}")
            pipeline_successful = False

        # Step 5: Plot Generation (optional)
        logger.info("--- Step 5: Plot Generation ---")
        try:
            if not run_plotting(config):
                logger.warning("Step 5: Error generating plots.")
                pipeline_successful = False  # Mark as not fully successful
            else:
                logger.info("Step 5 completed successfully.")
        except Exception as e:
            logger.warning(f"Step 5: Error generating plots: {e}")
            pipeline_successful = False

        if pipeline_successful:
            logger.info(f"Dataset {dataset_name or 'single dataset'} processed successfully.")
        else:
            logger.warning(f"Dataset {dataset_name or 'single dataset'} completed with warnings or errors in optional steps.")

    except Exception as e:
        logger.error(f"Unexpected error processing dataset {dataset_name or 'single dataset'}: {e}", exc_info=True)
        return False

    return pipeline_successful


def run_batch_pipeline(config: Dict[str, Any]) -> bool:
    """
    Execute the pipeline for multiple datasets in batch mode.

    Args:
        config: Configuration dictionary with batch settings

    Returns:
        True if batch processing completed successfully, False otherwise.
    """
    logger.info("Starting batch processing mode")

    try:
        # Validate batch configuration
        validate_batch_config(config)

        # Setup batch processing infrastructure
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        base_output_dir = config.get('batch_settings', {}).get('output_base_directory', 'batch_outputs')

        file_manager = BatchFileManager(base_output_dir, timestamp)
        file_manager.setup_batch_directory()

        datasets = config.get('datasets', [])
        progress_tracker = BatchProgressTracker(len(datasets))
        memory_manager = BatchMemoryManager()

        # Initialize batch results tracking
        batch_results = []
        batch_summary = BatchSummary(
            total_datasets=len(datasets),
            batch_output_directory=str(file_manager.batch_dir)
        )

        continue_on_error = config.get('continue_on_error', True)

        # Process each dataset
        for i, dataset_config in enumerate(datasets):
            dataset_name = dataset_config.get('name', f'dataset_{i+1}')
            progress_tracker.start_dataset(dataset_name)

            # Create batch result for this dataset
            result = BatchResult(
                dataset_name=dataset_name,
                success=False,
                start_time=datetime.datetime.now()
            )

            try:
                # Setup dataset-specific output directory
                dataset_dir = file_manager.setup_dataset_directory(dataset_name)
                result.output_directory = str(dataset_dir)

                # Merge global and dataset-specific configurations
                merged_config = merge_dataset_config(config, dataset_config)

                # Update output paths for this dataset
                dataset_output_paths = file_manager.get_dataset_output_paths(dataset_name)
                merged_config['paths'].update(dataset_output_paths)

                # Check memory before processing
                memory_manager.check_memory_usage()

                # Execute pipeline for this dataset
                success = run_single_dataset_pipeline(merged_config, dataset_name)

                result.success = success
                result.end_time = datetime.datetime.now()

                if success:
                    batch_summary.successful_datasets.append(dataset_name)
                    # Record GeoTIFF path if created
                    geotiff_filename = dataset_output_paths['georeferenced_rgb_tif']
                    result.geotiff_path = str(dataset_dir / geotiff_filename)
                    logger.info(f"Dataset {dataset_name} completed successfully")
                else:
                    batch_summary.partial_failures.append(dataset_name)
                    logger.warning(f"Dataset {dataset_name} completed with errors")

                # Cleanup memory between datasets
                memory_manager.cleanup_dataset_memory()

            except Exception as e:
                result.success = False
                result.end_time = datetime.datetime.now()
                result.error_message = str(e)
                batch_summary.failed_datasets.append(dataset_name)

                logger.error(f"Error processing dataset {dataset_name}: {e}")

                if not continue_on_error:
                    logger.error("Stopping batch processing due to error")
                    break

            finally:
                batch_results.append(result)

        # Finalize batch summary
        batch_summary.end_time = datetime.datetime.now()

        # Save batch summary
        summary_path = file_manager.get_batch_summary_path()
        save_batch_summary(batch_summary, batch_results, summary_path)

        # Log final results
        logger.info(f"Batch processing completed:")
        logger.info(f"  Total datasets: {batch_summary.total_datasets}")
        logger.info(f"  Successful: {len(batch_summary.successful_datasets)}")
        logger.info(f"  Failed: {len(batch_summary.failed_datasets)}")
        logger.info(f"  Partial failures: {len(batch_summary.partial_failures)}")
        logger.info(f"  Success rate: {batch_summary.success_rate:.1f}%")
        logger.info(f"  Total duration: {batch_summary.total_duration_seconds / 60:.1f} minutes")
        logger.info(f"  Batch summary saved to: {summary_path}")

        # Return True if at least some datasets were successful
        return len(batch_summary.successful_datasets) > 0

    except Exception as e:
        logger.error(f"Error in batch processing: {e}", exc_info=True)
        return False


def run_complete_pipeline(config_path: str = 'config.toml') -> bool:
    """
    Execute the complete HSI Direct Georeferencing Pipeline.

    Supports both single dataset and batch processing modes based on configuration.

    Args:
        config_path: Path to the TOML configuration file. Default is 'config.toml'.

    Returns:
        True if the pipeline completed successfully, False otherwise.
    """

    # Setup logging first
    setup_logging(log_level="INFO", log_file="pipeline.log")

    logger.info(f"Starting complete HSI Georeferencing Pipeline with configuration: {config_path}")

    try:
        # Load configuration once
        config = load_pipeline_config(config_path)

        # Check if batch processing is enabled
        if config.get('batch_mode', False):
            logger.info("Batch processing mode detected")
            return run_batch_pipeline(config)
        else:
            logger.info("Single dataset processing mode")
            return run_single_dataset_pipeline(config)

    except PipelineConfigError as e:
        logger.error(f"Configuration error: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error in pipeline: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    # Future enhancement: Add command-line arguments for config_path or step selection
    # Example with argparse:
    # import argparse
    # parser = argparse.ArgumentParser(description="Execute the HSI Georeferencing Pipeline.")
    # parser.add_argument('--config', type=str, default='config.toml',
    #                     help='Path to configuration file (default: config.toml)')
    # args = parser.parse_args()
    # run_complete_pipeline(config_path=args.config)

    success = run_complete_pipeline()
    if success:
        print("Pipeline execution completed: Success")
    else:
        print("Pipeline execution completed: With errors/warnings")