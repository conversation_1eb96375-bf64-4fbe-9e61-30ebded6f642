import rasterio
from rasterio.transform import Affine
import numpy as np
import pandas as pd
from scipy.spatial import KDTree
import spectral # For reading .hdr and .img files
import toml # For reading config file
import os   # For path operations
import datetime # For timestamp
from typing import Union, Dict, Any

from .logging_config import get_logger
from .pipeline_exceptions import PipelineConfigError

# --- Global constants (can be moved inside function if preferred, but NO_DATA_VALUE is simple) ---
NO_DATA_VALUE = 0

def find_nearest_band_index(wavelengths_list, target_wavelength):
    """Finds the index of the band closest to the target wavelength."""
    wavelengths_array = np.array([float(w) for w in wavelengths_list])
    idx = (np.abs(wavelengths_array - target_wavelength)).argmin()
    return idx

def normalize_band(band_data, method="min_max", no_data_val=0):
    """Normalizes a band to 0-255 (uint8) based on the specified method, excluding no_data_val."""
    
    is_no_data_mask = (band_data == no_data_val)
    is_nan_mask = np.isnan(band_data)
    valid_data_mask = ~is_no_data_mask & ~is_nan_mask
    
    valid_data = band_data[valid_data_mask]

    if valid_data.size == 0: # All data is no_data_val or NaN
        return np.full_like(band_data, no_data_val, dtype=np.uint8)

    if method == "min_max":
        min_val = np.min(valid_data)
        max_val = np.max(valid_data)
    elif method == "percentile_2_98":
        min_val = np.percentile(valid_data, 2)
        max_val = np.percentile(valid_data, 98)
    else:
        raise ValueError(f"Unsupported normalization method '{method}'. Supported: 'min_max', 'percentile_2_98'.")

    normalized_band_data = np.zeros_like(band_data, dtype=np.float32)

    if np.isclose(max_val, min_val):
        normalized_band_data[valid_data_mask] = 0.0
    else:
        normalized_values = 255.0 * (valid_data - min_val) / (max_val - min_val)
        normalized_band_data[valid_data_mask] = normalized_values
    
    normalized_band_data = np.clip(normalized_band_data, 0, 255)
    
    output_uint8 = normalized_band_data.astype(np.uint8)
    
    output_uint8[is_no_data_mask] = np.uint8(no_data_val)
    output_uint8[is_nan_mask] = np.uint8(no_data_val)

    return output_uint8

def run_create_rgb_geotiff(config_or_path: Union[Dict[str, Any], str], dataset_name: str = None) -> bool:
    """
    Creates a georeferenced RGB GeoTIFF from HSI data based on configuration.

    Args:
        config_or_path: Either a configuration dictionary or path to TOML file.
        dataset_name: Optional dataset name for batch processing. Used for unique file naming.

    Returns:
        True if the GeoTIFF was created successfully, False otherwise.

    Raises:
        PipelineConfigError: If configuration is invalid or cannot be loaded
    """
    logger = get_logger(__name__)

    # --- Configuration Loading/Validation ---
    if isinstance(config_or_path, dict):
        config = config_or_path
        logger.info("Using provided configuration dictionary for GeoTIFF creation")
    elif isinstance(config_or_path, str):
        logger.info(f"Loading configuration from file: {config_or_path}")
        try:
            config = toml.load(config_or_path)
        except FileNotFoundError:
            raise PipelineConfigError(f"Configuration file not found: {config_or_path}")
        except Exception as e:
            raise PipelineConfigError(f"Error loading configuration file: {e}")
    else:
        raise PipelineConfigError(f"Invalid config parameter type: {type(config_or_path)}")

    # --- Paths from Configuration ---
    hsi_data_dir = config['paths']['hsi_data_directory']
    hsi_base_filename = config['paths']['hsi_base_filename']
    hdr_file_path = os.path.join(hsi_data_dir, f"{hsi_base_filename}.hdr")
    img_file_path = os.path.join(hsi_data_dir, f"{hsi_base_filename}.img")

    output_dir = config['paths']['output_directory']
    os.makedirs(output_dir, exist_ok=True) # Ensure output directory exists

    georeferenced_pixels_input_filename = config['paths']['georeferenced_pixels_csv']
    georeferenced_pixels_input_path = os.path.join(output_dir, georeferenced_pixels_input_filename)

    base_geotiff_output_filename = config['paths']['georeferenced_rgb_tif']
    timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
    name, ext = os.path.splitext(base_geotiff_output_filename)

    # Enhanced naming for batch processing with dataset-specific identifiers
    if dataset_name:
        # For batch processing: use dataset name in filename
        geotiff_output_filename_with_ts = f"georeferenced_rgb_{dataset_name}_{timestamp}{ext}"
        logger.info(f"Batch processing mode: Using dataset-specific filename for {dataset_name}")
    else:
        # For single dataset processing: use original naming scheme
        geotiff_output_filename_with_ts = f"{name}_{timestamp}{ext}"
        logger.info("Single dataset processing mode: Using standard filename")

    geotiff_output_path = os.path.join(output_dir, geotiff_output_filename_with_ts)

    # --- Parameters from Configuration ---
    target_R_nm = config['parameters']['rgb_geotiff_creation']['target_wavelength_R_nm']
    target_G_nm = config['parameters']['rgb_geotiff_creation']['target_wavelength_G_nm']
    target_B_nm = config['parameters']['rgb_geotiff_creation']['target_wavelength_B_nm']

    TARGET_WAVELENGTHS_NM = {
        'R': target_R_nm,
        'G': target_G_nm,
        'B': target_B_nm
    }

    TARGET_RESOLUTION_METERS = config['parameters']['rgb_geotiff_creation']['target_resolution_meters']

    output_epsg_code_val = config['parameters']['rgb_geotiff_creation']['output_epsg_code']
    if isinstance(output_epsg_code_val, (int, float)):
        TARGET_CRS = f"EPSG:{int(output_epsg_code_val)}"
    elif isinstance(output_epsg_code_val, str) and not output_epsg_code_val.upper().startswith("EPSG:"):
        TARGET_CRS = f"EPSG:{output_epsg_code_val}"
    else:
        TARGET_CRS = str(output_epsg_code_val)
    
    normalization_method = config['parameters']['rgb_geotiff_creation']['normalization_method']

    # 1. Load HSI data and band selection
    logger.info(f"Loading HSI data from {hdr_file_path} and {img_file_path}...")
    try:
        img_hsi = spectral.open_image(hdr_file_path)
        hsi_data = img_hsi.load() # Loads data as (lines, samples, bands)

        hsi_lines = img_hsi.shape[0]
        hsi_samples = img_hsi.shape[1]
        hsi_num_bands = img_hsi.shape[2]

        if hasattr(img_hsi, 'bands') and hasattr(img_hsi.bands, 'centers'):
            available_wavelengths = [float(w) for w in img_hsi.bands.centers]
        elif 'wavelength' in img_hsi.metadata:
            available_wavelengths_str = img_hsi.metadata['wavelength']
            available_wavelengths = [float(w) for w in available_wavelengths_str]
        else:
            raise ValueError("Wavelength information not found in HSI header metadata or bands attribute.")

        logger.info(f"HSI data dimensions: Lines={hsi_lines}, Samples={hsi_samples}, Bands={hsi_num_bands}")
        if available_wavelengths:
            logger.info(f"First 10 available wavelengths: {available_wavelengths[:10]} nm")
        else:
            logger.warning("No wavelengths could be extracted.")

    except FileNotFoundError:
        error_msg = f"HSI file not found. Searched for .hdr at '{hdr_file_path}' and .img (implicitly by spectral). Please ensure the HSI header and image files are correctly specified and accessible."
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)
    except Exception as e:
        error_msg = f"Error loading HSI data: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    band_indices = {}
    for color, target_wl in TARGET_WAVELENGTHS_NM.items():
        band_indices[color] = find_nearest_band_index(available_wavelengths, target_wl)
        logger.info(f"Target {color} ({target_wl}nm) -> Closest band index: {band_indices[color]} (Wavelength: {available_wavelengths[band_indices[color]]:.2f}nm)")

    # 2. Define georeferencing parameters and target raster
    logger.info(f"Loading georeferencing data from {georeferenced_pixels_input_path}...")
    try:
        geo_df = pd.read_csv(georeferenced_pixels_input_path)
    except FileNotFoundError:
        error_msg = f"Georeferencing CSV file not found at '{georeferenced_pixels_input_path}'"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)
    except Exception as e:
        error_msg = f"Error reading CSV {georeferenced_pixels_input_path}: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    if geo_df.empty:
        error_msg = f"Georeferencing CSV file '{georeferenced_pixels_input_path}' is empty"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    min_x, max_x = geo_df['X_ground'].min(), geo_df['X_ground'].max()
    min_y, max_y = geo_df['Y_ground'].min(), geo_df['Y_ground'].max()
    logger.info(f"Bounding Box (X_ground, Y_ground): Min=({min_x:.2f}, {min_y:.2f}), Max=({max_x:.2f}, {max_y:.2f})")

    target_width = int(np.ceil((max_x - min_x) / TARGET_RESOLUTION_METERS))
    target_height = int(np.ceil((max_y - min_y) / TARGET_RESOLUTION_METERS))

    if target_width <= 0 or target_height <= 0:
        error_msg = f"Invalid target dimensions calculated. Width={target_width}, Height={target_height}. This might be due to issues in georeferenced_pixels.csv (e.g., all points are identical or min/max are swapped)."
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)
    logger.info(f"Target GeoTIFF dimensions: Width={target_width}px, Height={target_height}px")

    transform = Affine(TARGET_RESOLUTION_METERS, 0.0, min_x,
                       0.0, -TARGET_RESOLUTION_METERS, max_y)
    logger.info(f"GeoTransform: {transform}")

    # 3. Resample HSI data to target raster (Nearest Neighbour)
    logger.info("Resampling HSI data to target raster...")
    target_rgb_image = np.full((target_height, target_width, 3), NO_DATA_VALUE, dtype=np.float32)

    source_coords = geo_df[['X_ground', 'Y_ground']].values
    try:
        kdtree = KDTree(source_coords)
    except Exception as e:
        error_msg = f"Error creating KDTree (likely from empty or invalid source_coords): {e}"
        if source_coords.shape[0] == 0:
            error_msg += " The georeferenced_pixels.csv might be empty or failed to load properly."
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    for r in range(target_height):
        if r % (max(1, target_height // 10)) == 0 : # Log progress, ensure divisor is at least 1
             logger.info(f"Resampling progress: {r/target_height*100:.1f}%")
        for c in range(target_width):
            target_x = min_x + (c + 0.5) * TARGET_RESOLUTION_METERS
            target_y = max_y - (r + 0.5) * TARGET_RESOLUTION_METERS

            _, nearest_idx = kdtree.query([target_x, target_y], k=1)

            if nearest_idx < 0 or nearest_idx >= len(geo_df): # Check if index is valid
                # This case should ideally not happen with KDTree query if source_coords is not empty
                logger.warning(f"Invalid nearest_idx {nearest_idx} from KDTree query. Skipping pixel ({r},{c}).")
                continue

            hsi_line = geo_df.loc[nearest_idx, 'hsi_line_index']
            hsi_pixel = geo_df.loc[nearest_idx, 'pixel_index']

            if 0 <= hsi_line < hsi_data.shape[0] and 0 <= hsi_pixel < hsi_data.shape[1]:
                r_val = hsi_data[hsi_line, hsi_pixel, band_indices['R']]
                g_val = hsi_data[hsi_line, hsi_pixel, band_indices['G']]
                b_val = hsi_data[hsi_line, hsi_pixel, band_indices['B']]

                target_rgb_image[r, c, 0] = r_val
                target_rgb_image[r, c, 1] = g_val
                target_rgb_image[r, c, 2] = b_val
    logger.info("Resampling complete.")

    # 4. Normalize RGB bands
    logger.info(f"Normalizing RGB bands using method: {normalization_method}...")
    normalized_rgb_image = np.zeros_like(target_rgb_image, dtype=np.uint8)
    for i in range(3):
        band_data = target_rgb_image[:, :, i]
        if np.all( (band_data == NO_DATA_VALUE) | np.isnan(band_data) ):
            logger.warning(f"Band {i} contains only NoData or NaN values. It will be all {NO_DATA_VALUE} in the output.")
            normalized_rgb_image[:, :, i] = NO_DATA_VALUE
        else:
            try:
                normalized_rgb_image[:, :, i] = normalize_band(band_data, method=normalization_method, no_data_val=NO_DATA_VALUE)
            except ValueError as e:
                error_msg = f"Error during normalization of band {i}: {e}"
                logger.error(error_msg)
                raise PipelineConfigError(error_msg)
    logger.info("Normalization complete.")

    # 5. Save GeoTIFF
    logger.info(f"Saving GeoTIFF to {geotiff_output_path}...")
    try:
        with rasterio.open(
            geotiff_output_path,
            'w',
            driver='GTiff',
            height=target_height,
            width=target_width,
            count=3, # R, G, B
            dtype=rasterio.uint8,
            crs=TARGET_CRS,
            transform=transform,
            nodata=NO_DATA_VALUE if isinstance(NO_DATA_VALUE, (int, float)) else None
        ) as dst:
            dst.write(normalized_rgb_image.transpose(2, 0, 1))
        logger.info(f"GeoTIFF successfully saved to {geotiff_output_path}")
    except Exception as e:
        error_msg = f"Error saving GeoTIFF: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    logger.info("Processing finished successfully.")
    return True

if __name__ == "__main__":
    DEFAULT_CONFIG_PATH = 'config.toml'
    logger = get_logger(__name__)
    logger.info(f"Creating georeferenced RGB GeoTIFF with configuration: {DEFAULT_CONFIG_PATH}")

    # Ensure the default config file exists before trying to run
    if not os.path.exists(DEFAULT_CONFIG_PATH):
        logger.error(f"Default configuration file '{DEFAULT_CONFIG_PATH}' not found.")
        logger.error("Please ensure the file exists or provide a valid path when using this script as a module.")
    else:
        try:
            success = run_create_rgb_geotiff(config_or_path=DEFAULT_CONFIG_PATH)
            if success:
                logger.info("Georeferenced RGB GeoTIFF created successfully.")
            else:
                logger.error("Error creating georeferenced RGB GeoTIFF.")
        except PipelineConfigError as e:
            logger.error(f"Configuration error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)