# HSI Pipeline Configuration Loading Issue Analysis

## Executive Summary

The HSI Georeferencing Pipeline fails during Steps 4 (GeoTIFF Creation) and 5 (Plot Generation) due to **parameter type mismatches** in configuration handling. The main pipeline loads `config.toml` into a dictionary and passes this dictionary to downstream functions, but these functions expect a file path string instead.

**Root Cause**: Function signature inconsistency between main pipeline (passes `dict`) and sub-modules (expect `str`).

---

## Section 1: Exact Problematic Code Snippets

### 1.1 Main Pipeline Configuration Flow

**File**: `DG_MVP_4_critic/src/hsi_pipeline/main_pipeline.py`

<augment_code_snippet path="DG_MVP_4_critic/src/hsi_pipeline/main_pipeline.py" mode="EXCERPT">
````python
# Lines 87-88: Config loaded as dictionary
config = load_pipeline_config(config_path)

# Lines 123 & 135: Dictionary passed to functions expecting string
if not run_create_rgb_geotiff(config):  # ❌ Passes dict, expects str
if not run_plotting(config):            # ❌ Passes dict, expects str
````
</augment_code_snippet>

### 1.2 GeoTIFF Creation Module - Problematic Function Signature

**File**: `DG_MVP_4_critic/src/hsi_pipeline/create_georeferenced_rgb.py`

<augment_code_snippet path="DG_MVP_4_critic/src/hsi_pipeline/create_georeferenced_rgb.py" mode="EXCERPT">
````python
# Lines 58-72: Function expects string but receives dict
def run_create_rgb_geotiff(config_path: str) -> bool:
    """
    Args:
        config_path: Path to the TOML configuration file.  # ❌ Expects string
    """
    try:
        config = toml.load(config_path)  # ❌ Fails when config_path is dict
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False
````
</augment_code_snippet>

### 1.3 Plotting Module - Problematic Function Signature

**File**: `DG_MVP_4_critic/src/hsi_pipeline/plotting/plot_hsi_data.py`

<augment_code_snippet path="DG_MVP_4_critic/src/hsi_pipeline/plotting/plot_hsi_data.py" mode="EXCERPT">
````python
# Lines 108-115: Function expects string but receives dict
def run_plotting(config_path: str) -> bool:
    try:
        config = toml.load(config_path)  # ❌ Fails when config_path is dict
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False
````
</augment_code_snippet>

---

## Section 2: Detailed Root Cause Analysis

### 2.1 Configuration Flow Breakdown

1. **Main Pipeline** (`main_pipeline.py:88`):
   - Loads `config.toml` using `load_pipeline_config()` → Returns `Dict[str, Any]`
   - Stores result in `config` variable (dictionary)

2. **Step 4 Call** (`main_pipeline.py:123`):
   - Calls `run_create_rgb_geotiff(config)` → Passes dictionary
   - Function signature expects `config_path: str` → Type mismatch

3. **Step 5 Call** (`main_pipeline.py:135`):
   - Calls `run_plotting(config)` → Passes dictionary  
   - Function signature expects `config_path: str` → Type mismatch

### 2.2 Error Generation Mechanism

When `toml.load()` receives a dictionary instead of a file path:
```python
toml.load({"paths": {...}, "parameters": {...}})  # ❌ Invalid input type
```

The TOML library raises: `"You can only load a file descriptor, filename or list"`

### 2.3 Inconsistency with Other Modules

**Working Modules** (Steps 1-3):
- `run_consolidation(config)` - Accepts `dict` ✅
- `run_synchronization(config)` - Accepts `dict` ✅  
- `run_georeferencing(config)` - Accepts `dict` ✅

**Broken Modules** (Steps 4-5):
- `run_create_rgb_geotiff(config_path)` - Expects `str` ❌
- `run_plotting(config_path)` - Expects `str` ❌

---

## Section 3: Proposed `str_replace` Patches

### 3.1 Fix for GeoTIFF Creation Module

**File**: `DG_MVP_4_critic/src/hsi_pipeline/create_georeferenced_rgb.py`

```python
# BEFORE (Lines 58-78):
def run_create_rgb_geotiff(config_path: str) -> bool:
    """
    Creates a georeferenced RGB GeoTIFF from HSI data based on a configuration file.

    Args:
        config_path: Path to the TOML configuration file.

    Returns:
        True if the GeoTIFF was created successfully, False otherwise.
    """
    print(f"Starting GeoTIFF generation process using config: {config_path}...")

    # --- Configuration Loading ---
    try:
        config = toml.load(config_path)
    except FileNotFoundError:
        print(f"Fehler: Konfigurationsdatei {config_path} nicht gefunden.")
        return False
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False

# AFTER:
def run_create_rgb_geotiff(config_or_path) -> bool:
    """
    Creates a georeferenced RGB GeoTIFF from HSI data based on configuration.

    Args:
        config_or_path: Either a configuration dictionary or path to TOML file.

    Returns:
        True if the GeoTIFF was created successfully, False otherwise.
    """
    logger = get_logger(__name__)
    
    # --- Configuration Loading/Validation ---
    if isinstance(config_or_path, dict):
        config = config_or_path
        logger.info("Using provided configuration dictionary for GeoTIFF creation")
    elif isinstance(config_or_path, str):
        logger.info(f"Loading configuration from file: {config_or_path}")
        try:
            config = toml.load(config_or_path)
        except FileNotFoundError:
            raise PipelineConfigError(f"Configuration file not found: {config_or_path}")
        except Exception as e:
            raise PipelineConfigError(f"Error loading configuration file: {e}")
    else:
        raise PipelineConfigError(f"Invalid config parameter type: {type(config_or_path)}")
```

### 3.2 Fix for Plotting Module

**File**: `DG_MVP_4_critic/src/hsi_pipeline/plotting/plot_hsi_data.py`

```python
# BEFORE (Lines 108-116):
def run_plotting(config_path: str) -> bool:
    try:
        config = toml.load(config_path)
    except FileNotFoundError:
        print(f"Fehler: Konfigurationsdatei {config_path} nicht gefunden.")
        return False
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False

# AFTER:
def run_plotting(config_or_path) -> bool:
    """
    Generate HSI data visualization plots.

    Args:
        config_or_path: Either a configuration dictionary or path to TOML file.

    Returns:
        True if plots were generated successfully, False otherwise.
    """
    logger = get_logger(__name__)
    
    # --- Configuration Loading/Validation ---
    if isinstance(config_or_path, dict):
        config = config_or_path
        logger.info("Using provided configuration dictionary for plotting")
    elif isinstance(config_or_path, str):
        logger.info(f"Loading configuration from file: {config_or_path}")
        try:
            config = toml.load(config_or_path)
        except FileNotFoundError:
            raise PipelineConfigError(f"Configuration file not found: {config_or_path}")
        except Exception as e:
            raise PipelineConfigError(f"Error loading configuration file: {e}")
    else:
        raise PipelineConfigError(f"Invalid config parameter type: {type(config_or_path)}")
```

---

## Section 4: Test Strategy Following TDD Methodology

### 4.1 Unit Test Requirements

**Test File**: `DG_MVP_4_critic/tests/test_config_handling.py`

```python
class TestConfigHandling:
    def test_geotiff_creation_accepts_config_dict(self):
        """Given valid config dict, When calling run_create_rgb_geotiff, Then should process successfully"""
        
    def test_geotiff_creation_accepts_config_path(self):
        """Given valid config file path, When calling run_create_rgb_geotiff, Then should load and process"""
        
    def test_plotting_accepts_config_dict(self):
        """Given valid config dict, When calling run_plotting, Then should process successfully"""
        
    def test_plotting_accepts_config_path(self):
        """Given valid config file path, When calling run_plotting, Then should load and process"""
        
    def test_invalid_config_type_raises_exception(self):
        """Given invalid config type, When calling functions, Then should raise PipelineConfigError"""
```

### 4.2 Integration Test Requirements

**Test File**: `DG_MVP_4_critic/tests/test_pipeline_integration.py`

```python
class TestPipelineIntegration:
    def test_complete_pipeline_config_flow(self):
        """Given config.toml, When running complete pipeline, Then Steps 4-5 should receive dict correctly"""
        
    def test_standalone_module_execution(self):
        """Given config file path, When running modules standalone, Then should work with file path"""
```

---

## Section 5: Implementation Plan

### 5.1 Implementation Steps (Following LS2+ Standards)

1. **Import Required Modules**:
   - Add `from .logging_config import get_logger` 
   - Add `from .pipeline_exceptions import PipelineConfigError`

2. **Update Function Signatures**:
   - Change parameter names from `config_path` to `config_or_path`
   - Remove type hints to allow both `dict` and `str`

3. **Add Type Checking Logic**:
   - Use `isinstance()` to determine input type
   - Handle both dictionary and string inputs gracefully

4. **Replace Print Statements**:
   - Convert all `print()` calls to `logger.info/warning/error()`
   - Follow LS2+ standard for logging

5. **Translate German Text**:
   - Convert all German error messages to English
   - Update user-facing messages

6. **Exception Handling**:
   - Replace `return False` with custom exceptions
   - Use `PipelineConfigError` for configuration issues

### 5.2 Backward Compatibility

- ✅ Standalone execution (with file paths) continues working
- ✅ Pipeline execution (with dictionaries) now works correctly  
- ✅ Existing tests remain functional
- ✅ No breaking changes to public interfaces

### 5.3 Code Quality Assurance

- ✅ Modules remain under 500 lines
- ✅ Follow established coding standards from LS layers
- ✅ Comprehensive error handling with custom exceptions
- ✅ Proper logging instead of print statements
- ✅ English-only text in user messages

---

## Success Criteria Verification

- [ ] Steps 4-5 execute without configuration loading errors
- [ ] Both standalone and pipeline execution modes work
- [ ] All existing functionality preserved
- [ ] TDD test coverage for new configuration handling
- [ ] German text translated to English
- [ ] Logger calls replace print statements
- [ ] Custom exceptions replace return False patterns
- [ ] Modules accept config dict instead of config_path string
