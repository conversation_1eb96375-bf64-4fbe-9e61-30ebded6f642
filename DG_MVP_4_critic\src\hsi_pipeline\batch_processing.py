"""
Batch processing functionality for HSI Georeferencing Pipeline.

This module provides infrastructure for processing multiple HSI datasets
sequentially in a single pipeline run, with individual GeoTIFF generation
for each dataset.

Key Features:
- Dataset-specific output directories and file naming
- Individual GeoTIFF generation per dataset
- Error handling and recovery for batch operations
- Progress tracking across multiple datasets
- Memory management between datasets
"""

import os
import time
import datetime
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass, field
import json
import copy

from .logging_config import get_logger
from .pipeline_exceptions import PipelineConfigError


@dataclass
class BatchResult:
    """Result information for a single dataset in batch processing."""
    dataset_name: str
    success: bool
    start_time: datetime.datetime
    end_time: Optional[datetime.datetime] = None
    failed_steps: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    output_directory: Optional[str] = None
    geotiff_path: Optional[str] = None
    
    @property
    def duration_seconds(self) -> float:
        """Calculate processing duration in seconds."""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


@dataclass
class BatchSummary:
    """Summary of batch processing results."""
    total_datasets: int
    successful_datasets: List[str] = field(default_factory=list)
    failed_datasets: List[str] = field(default_factory=list)
    partial_failures: List[str] = field(default_factory=list)
    start_time: datetime.datetime = field(default_factory=datetime.datetime.now)
    end_time: Optional[datetime.datetime] = None
    batch_output_directory: str = ""
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate as percentage."""
        if self.total_datasets == 0:
            return 0.0
        return len(self.successful_datasets) / self.total_datasets * 100
    
    @property
    def total_duration_seconds(self) -> float:
        """Calculate total batch processing duration."""
        if self.end_time and self.start_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


class BatchFileManager:
    """Manages file paths and directory structure for batch processing."""
    
    def __init__(self, base_output_dir: str, timestamp: str):
        self.base_dir = Path(base_output_dir)
        self.timestamp = timestamp
        self.batch_dir = self.base_dir / f"batch_{timestamp}"
        self.logger = get_logger(__name__)
        
    def setup_batch_directory(self) -> Path:
        """Create and return the main batch output directory."""
        self.batch_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"Created batch output directory: {self.batch_dir}")
        return self.batch_dir
        
    def setup_dataset_directory(self, dataset_name: str) -> Path:
        """Create and return dataset-specific output directory."""
        dataset_dir = self.batch_dir / dataset_name
        dataset_dir.mkdir(parents=True, exist_ok=True)
        
        # Create plots subdirectory
        plots_dir = dataset_dir / "plots"
        plots_dir.mkdir(exist_ok=True)
        
        self.logger.info(f"Created dataset directory: {dataset_dir}")
        return dataset_dir
        
    def get_dataset_output_paths(self, dataset_name: str) -> Dict[str, str]:
        """Generate all output file paths for a dataset."""
        dataset_dir = self.batch_dir / dataset_name
        
        return {
            'output_directory': str(dataset_dir),
            'consolidated_webodm_poses_csv': "webodm_poses_consolidated.csv",
            'hsi_poses_csv': "hsi_poses.csv", 
            'georeferenced_pixels_csv': "georeferenced_pixels.csv",
            'georeferenced_rgb_tif': f"georeferenced_rgb_{dataset_name}_{self.timestamp}.tif",
            'plots_directory': str(dataset_dir / "plots")
        }
    
    def get_batch_summary_path(self) -> str:
        """Get path for batch summary JSON file."""
        return str(self.batch_dir / f"batch_summary_{self.timestamp}.json")
    
    def get_batch_log_path(self) -> str:
        """Get path for batch log file."""
        return str(self.batch_dir / f"batch_log_{self.timestamp}.log")


class BatchProgressTracker:
    """Tracks progress across multiple datasets and pipeline steps."""
    
    def __init__(self, total_datasets: int):
        self.total_datasets = total_datasets
        self.current_dataset = 0
        self.current_step = 0
        self.total_steps = 5  # WebODM, HSI Sync, Georef, GeoTIFF, Plots
        self.start_time = time.time()
        self.logger = get_logger(__name__)
        
    def start_dataset(self, dataset_name: str):
        """Mark the start of processing for a new dataset."""
        self.current_dataset += 1
        self.current_step = 0
        overall_progress = (self.current_dataset - 1) / self.total_datasets * 100
        self.logger.info(f"Starting dataset {self.current_dataset}/{self.total_datasets}: {dataset_name} (Overall: {overall_progress:.1f}%)")
        
    def start_step(self, step_name: str):
        """Mark the start of a new pipeline step."""
        self.current_step += 1
        step_progress = self.current_step / self.total_steps * 100
        overall_progress = ((self.current_dataset - 1) * self.total_steps + self.current_step) / (self.total_datasets * self.total_steps) * 100
        self.logger.info(f"Dataset {self.current_dataset}/{self.total_datasets}, Step {self.current_step}/{self.total_steps}: {step_name} (Step: {step_progress:.1f}%, Overall: {overall_progress:.1f}%)")
        
    def estimate_remaining_time(self) -> str:
        """Estimate remaining processing time based on current progress."""
        elapsed = time.time() - self.start_time
        completed_units = (self.current_dataset - 1) * self.total_steps + self.current_step
        total_units = self.total_datasets * self.total_steps
        
        if completed_units > 0:
            avg_time_per_unit = elapsed / completed_units
            remaining_units = total_units - completed_units
            estimated_remaining = remaining_units * avg_time_per_unit
            return f"{estimated_remaining / 60:.1f} minutes"
        return "Unknown"


class BatchMemoryManager:
    """Manages memory usage during batch processing."""
    
    def __init__(self, max_memory_gb: float = 8.0):
        self.max_memory_bytes = max_memory_gb * 1024**3
        self.shared_cache = {}
        self.logger = get_logger(__name__)
        
    def cache_shared_data(self, key: str, data: Any):
        """Cache data that can be shared across datasets (e.g., DSM)."""
        self.shared_cache[key] = data
        self.logger.debug(f"Cached shared data: {key}")
        
    def get_cached_data(self, key: str):
        """Retrieve cached shared data."""
        return self.shared_cache.get(key)
        
    def check_memory_usage(self):
        """Monitor memory usage and warn if approaching limits."""
        try:
            import psutil
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > 80:
                self.logger.warning(f"High memory usage: {memory_percent}%. Consider processing fewer datasets simultaneously.")
        except ImportError:
            self.logger.debug("psutil not available for memory monitoring")
            
    def cleanup_dataset_memory(self):
        """Force garbage collection between datasets."""
        import gc
        gc.collect()
        self.logger.debug("Performed garbage collection")


def merge_dataset_config(global_config: Dict[str, Any], dataset_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Merge global configuration with dataset-specific configuration.
    
    Dataset-specific parameters override global parameters.
    """
    # Deep copy global config to avoid modifying original
    merged_config = copy.deepcopy(global_config)
    
    # Update paths with dataset-specific values
    if 'paths' in merged_config:
        for key, value in dataset_config.items():
            if key in ['hsi_data_directory', 'hsi_base_filename', 'webodm_data_directory', 'sensor_model_file']:
                merged_config['paths'][key] = value
    
    # Merge dataset-specific parameters if they exist
    if 'parameters' in dataset_config:
        if 'parameters' not in merged_config:
            merged_config['parameters'] = {}
        
        # Deep merge parameters
        for param_section, param_values in dataset_config['parameters'].items():
            if param_section not in merged_config['parameters']:
                merged_config['parameters'][param_section] = {}
            merged_config['parameters'][param_section].update(param_values)
    
    return merged_config


def validate_batch_config(config: Dict[str, Any]) -> None:
    """
    Validate batch processing configuration.
    
    Raises:
        PipelineConfigError: If configuration is invalid
    """
    if not config.get('batch_mode', False):
        raise PipelineConfigError("batch_mode must be set to true for batch processing")
    
    datasets = config.get('datasets', [])
    if not datasets:
        raise PipelineConfigError("No datasets specified in batch configuration")
    
    if not isinstance(datasets, list):
        raise PipelineConfigError("datasets must be a list")
    
    # Validate each dataset configuration
    for i, dataset in enumerate(datasets):
        if not isinstance(dataset, dict):
            raise PipelineConfigError(f"Dataset {i} must be a dictionary")
        
        required_fields = ['hsi_data_directory', 'hsi_base_filename', 'webodm_data_directory']
        for field in required_fields:
            if field not in dataset:
                raise PipelineConfigError(f"Dataset {i} missing required field: {field}")
        
        # Validate that dataset name is provided or can be generated
        if 'name' not in dataset:
            dataset['name'] = f"dataset_{i+1}"


def save_batch_summary(summary: BatchSummary, results: List[BatchResult], file_path: str):
    """Save batch processing summary to JSON file."""
    summary_data = {
        'batch_info': {
            'total_datasets': summary.total_datasets,
            'successful_datasets': summary.successful_datasets,
            'failed_datasets': summary.failed_datasets,
            'partial_failures': summary.partial_failures,
            'success_rate_percent': summary.success_rate,
            'start_time': summary.start_time.isoformat(),
            'end_time': summary.end_time.isoformat() if summary.end_time else None,
            'total_duration_seconds': summary.total_duration_seconds,
            'batch_output_directory': summary.batch_output_directory
        },
        'dataset_results': [
            {
                'dataset_name': result.dataset_name,
                'success': result.success,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'duration_seconds': result.duration_seconds,
                'failed_steps': result.failed_steps,
                'error_message': result.error_message,
                'output_directory': result.output_directory,
                'geotiff_path': result.geotiff_path
            }
            for result in results
        ]
    }
    
    with open(file_path, 'w') as f:
        json.dump(summary_data, f, indent=2)
