{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.8.2", "globals": "c522c70d8958b729279df4ecda413474", "files": {"z_1ab0a9e30448b8e0___init___py": {"hash": "4636e208e5f498a9a06f2754559b9b3c", "index": {"url": "z_1ab0a9e30448b8e0___init___py.html", "file": "src\\hsi_pipeline\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py": {"hash": "6984c904f330427e5dc687ca508b0e8a", "index": {"url": "z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html", "file": "src\\hsi_pipeline\\create_consolidated_webodm_poses.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 112, "n_excluded": 17, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_create_georeferenced_rgb_py": {"hash": "be8acc478b38f86dfec706d252a2bba3", "index": {"url": "z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html", "file": "src\\hsi_pipeline\\create_georeferenced_rgb.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 192, "n_excluded": 18, "n_missing": 103, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_georeference_hsi_pixels_py": {"hash": "dfe422fa375a1f62cfc3780c73a6f357", "index": {"url": "z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html", "file": "src\\hsi_pipeline\\georeference_hsi_pixels.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 419, "n_excluded": 17, "n_missing": 399, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_lever_arm_utils_py": {"hash": "994a2bd316f52c289200a79e06196842", "index": {"url": "z_1ab0a9e30448b8e0_lever_arm_utils_py.html", "file": "src\\hsi_pipeline\\lever_arm_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 36, "n_excluded": 0, "n_missing": 30, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_logging_config_py": {"hash": "281138b4c9ced9c1f7d589585061df2d", "index": {"url": "z_1ab0a9e30448b8e0_logging_config_py.html", "file": "src\\hsi_pipeline\\logging_config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 14, "n_excluded": 0, "n_missing": 7, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_main_pipeline_py": {"hash": "99faa872064f3a0d67652c449fec4a46", "index": {"url": "z_1ab0a9e30448b8e0_main_pipeline_py.html", "file": "src\\hsi_pipeline\\main_pipeline.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 83, "n_excluded": 6, "n_missing": 69, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_pipeline_exceptions_py": {"hash": "c0b3ee11f3c2400581dd40465b355f4e", "index": {"url": "z_1ab0a9e30448b8e0_pipeline_exceptions_py.html", "file": "src\\hsi_pipeline\\pipeline_exceptions.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 20, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c979b7456c4d1cc8___init___py": {"hash": "aab4b2f0ac7a0bc8d685980563edc26c", "index": {"url": "z_c979b7456c4d1cc8___init___py.html", "file": "src\\hsi_pipeline\\plotting\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_c979b7456c4d1cc8_plot_hsi_data_py": {"hash": "7ac1c18c74bf226d22d70401522fb5a3", "index": {"url": "z_c979b7456c4d1cc8_plot_hsi_data_py.html", "file": "src\\hsi_pipeline\\plotting\\plot_hsi_data.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 131, "n_excluded": 14, "n_missing": 86, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py": {"hash": "f181784b3f4e89e6e6cfe9f9f4eb598b", "index": {"url": "z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html", "file": "src\\hsi_pipeline\\synchronize_hsi_webodm.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 232, "n_excluded": 17, "n_missing": 217, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_1ab0a9e30448b8e0_vectorized_georef_py": {"hash": "ee25cfd063a161f208bf63a59ed0668d", "index": {"url": "z_1ab0a9e30448b8e0_vectorized_georef_py.html", "file": "src\\hsi_pipeline\\vectorized_georef.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 128, "n_excluded": 0, "n_missing": 117, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}