# Globale Einstellungen oder Projektinformationen (optional)
project_name = "HSI Direct Georeferencing PoC"

[paths]
# Basisverzeichnisse (relativ zum Projekt-Hauptverzeichnis)
hsi_data_directory = "data/HSI/"
webodm_data_directory = "data/WebODM/"
output_directory = "." # Hauptverzeichnis für die meisten Ausgaben
plot_output_directory = "plots/" # Unterverzeichnis für Plots, relativ zum output_directory

# HSI spezifische Dateinamen oder relative Pfade (Basis ist hsi_data_directory)
hsi_base_filename = "2025-05-15_08-27-39_cont" # Wird für .hdr, .img, .sync.txt verwendet
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# WebODM spezifische Dateinamen oder relative Pfade (Basis ist webodm_data_directory)
shots_geojson_file = "shots.geojson"
haip_files_subdirectory = "haip_files/" # Unterverzeichnis für .haip Dateien
dsm_file = "data/WebODM/dsm.tif"

# Namen der Zwischen- und Ausgabedateien (werden im output_directory gespeichert, falls nicht anders angegeben)
consolidated_webodm_poses_csv = "webodm_poses_consolidated.csv"
hsi_poses_csv = "hsi_poses.csv"
georeferenced_pixels_csv = "georeferenced_pixels.csv"
# Ein neuer Name, um Verwechslungen mit alten Dateien zu vermeiden, wenn das Skript mit Config läuft
georeferenced_rgb_tif = "georeferenced_rgb_via_config.tif"

[parameters.webodm_consolidation]
# Schlüssel für den Zeitstempel in den .haip Dateien
haip_timestamp_key = "rgb"

[parameters.georeferencing]
# Boresight-Korrekturwinkel in GRAD
boresight_roll_deg = 0 # 0.1
boresight_pitch_deg = 0 # -1.35
boresight_yaw_deg = 0 
# Sensor model parameters (optimized)
scale_vinkel_x = 1
offset_vinkel_x = 0

# Lever arm components (in meters)
lever_arm_x_m = 0.0
lever_arm_y_m = 0.0
lever_arm_z_m = 0.0

# Lever arm source selection
# Optionen: "config" (Standard - verwendet immer Config-Werte, auch wenn 0),
#           "hdr" (verwendet nur HDR-Werte),
#           "auto" (Config wenn nicht-null, sonst HDR)
lever_arm_source = "config"

# Methode zur Berechnung der Grundebenenhöhe Z_ground
# Optionen könnten sein: "avg_pose_z_minus_offset", "fixed_value", "dsm_path" (für später)
z_ground_calculation_method = "dsm_intersection"
# Offset in Metern, der vom Durchschnitt der Kamerahöhen abgezogen wird
z_ground_offset_meters = 20.0
# Fester Wert für Z_ground, falls z_ground_calculation_method = "fixed_value"
z_ground_fixed_value_meters = 100.0
# Pfad zu einem DSM, falls z_ground_calculation_method = "dsm_path" (optional, für spätere Erweiterung)
# dsm_file_path = "data/WebODM/dsm.tif"

# Parameter für Ray-DSM-Intersection
ray_dsm_max_search_dist_m = 2000.0 # Maximale Suchdistanz entlang des Strahls in Metern
ray_dsm_step_m = 5.0             # Anfängliche Schrittweite für Ray Marching in Metern
ray_dsm_bisection_tolerance_m = 0.01 # Toleranz für das Bisektionsverfahren in Metern (Genauigkeit des Schnittpunkts)

[parameters.rgb_geotiff_creation]
# Ziel-Wellenlängen in Nanometern für die RGB-Kanäle
target_wavelength_R_nm = 800.0
target_wavelength_G_nm = 700.0
target_wavelength_B_nm = 550.0

# Ziel-Auflösung für das GeoTIFF in Metern
target_resolution_meters = 0.1

# EPSG-Code für das Koordinatenbezugssystem des Ausgabe-GeoTIFFs
output_epsg_code = 32632 # UTM Zone 32N, WGS84

# Normalisierungsmethode für RGB-Bänder beim Erstellen des GeoTIFFs
# Mögliche Optionen: "min_max", "percentile_2_98" (streckt zwischen 2. und 98. Perzentil)
normalization_method = "min_max"

[parameters.plotting]
# Hier könnten spezifische Plot-Parameter definiert werden, falls zukünftig benötigt.
# Beispiel:
# create_position_plot = false
# create_trajectory_plot = false

[parameters.pipeline_options]
processing_mode = "vectorized"