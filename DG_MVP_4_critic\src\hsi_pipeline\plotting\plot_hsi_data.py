import pandas as pd
import matplotlib.pyplot as plt
import toml
import os
import numpy as np
from typing import Union, Dict, Any

from ..logging_config import get_logger
from ..pipeline_exceptions import PipelineConfigError

# Plotting functions modified to accept plot_output_dir
def plot_positions(df, plot_output_dir):
    """
    Plots camera positions (pos_x, pos_y, pos_z) over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['pos_x'], label='pos_x')
    plt.plot(df['hsi_line_index'], df['pos_y'], label='pos_y')
    plt.plot(df['hsi_line_index'], df['pos_z'], label='pos_z')
    plt.xlabel("HSI Line Index")
    plt.ylabel("Position [Pose Units]")
    plt.title("Camera Positions over HSI Line Index")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_positions_over_index.png"))
    plt.close()

def plot_trajectory_2d(df, plot_output_dir):
    """
    Plots the 2D camera trajectory (pos_y vs. pos_x).
    """
    plt.figure(figsize=(8, 8))
    plt.plot(df['pos_x'], df['pos_y'])
    plt.xlabel("Position X [Units]")
    plt.ylabel("Position Y [Units]")
    plt.title("2D Camera Trajectory (Top View)")
    plt.axis('equal') # Ensure aspect ratio is equal for a proper trajectory view
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_trajectory_2d.png"))
    plt.close()

def plot_orientations(df, plot_output_dir):
    """
    Plots camera orientation (quaternions) over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['quat_x'], label='quat_x')
    plt.plot(df['hsi_line_index'], df['quat_y'], label='quat_y')
    plt.plot(df['hsi_line_index'], df['quat_z'], label='quat_z')
    plt.plot(df['hsi_line_index'], df['quat_w'], label='quat_w')
    plt.xlabel("HSI Line Index")
    plt.ylabel("Quaternion Value")
    plt.title("Camera Orientation (Quaternions) over HSI Line Index")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_orientation_over_index.png"))
    plt.close()

def plot_interpolation_quality(df, plot_output_dir):
    """
    Plots the time differences to previous and next support poses over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))
    plt.plot(df['hsi_line_index'], df['time_diff_to_prev_ms'], label='Time diff to previous support pose')
    plt.plot(df['hsi_line_index'], df['time_diff_to_next_ms'], label='Time diff to next support pose')
    plt.xlabel("HSI Line Index")
    plt.ylabel("Time Distance [ms]")
    plt.title("Time Distances to Interpolation Support Poses")
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(plot_output_dir, "plot_interpolation_quality.png"))
    plt.close()

def plot_yaw_and_flight_direction(df, plot_output_dir):
    """
    Plots YAW angle and flight direction over hsi_line_index.
    """
    plt.figure(figsize=(12, 6))

    # Calculate YAW from quaternions
    q_w = df['quat_w']
    q_x = df['quat_x']
    q_y = df['quat_y']
    q_z = df['quat_z']
    # Yaw (psi) rotation around Z axis - calculated in radians
    yaw_rad = np.arctan2(2 * (q_w * q_z + q_x * q_y), 1 - 2 * (q_y**2 + q_z**2))
    yaw_deg = np.rad2deg(yaw_rad) # Convert to degrees

    # Calculate flight direction from positions - calculated in radians
    # Angle of the change in position (dy, dx), CCW from positive X-axis
    # diff() will introduce NaN at the first element, so we align data by dropping it or using .iloc[1:]
    d_pos_x = df['pos_x'].diff()
    d_pos_y = df['pos_y'].diff()

    # Flight direction as Azimuth (degrees clockwise from North, 0-360)
    flight_direction_rad_azimuth = np.arctan2(d_pos_x, d_pos_y)
    flight_direction_deg_azimuth = (np.rad2deg(flight_direction_rad_azimuth) + 360) % 360

    # Plotting
    # We use df['hsi_line_index'].iloc[1:] for flight_direction because diff() reduces length by 1
    plt.plot(df['hsi_line_index'], yaw_deg, label='YAW Angle (° CCW from sensor X)')
    if not flight_direction_deg_azimuth.empty: # Ensure there's data to plot
        plt.plot(df['hsi_line_index'].iloc[1:], flight_direction_deg_azimuth.iloc[1:], label='Flight Direction (Azimuth ° CW from N)', linestyle='--')

    plt.xlabel("HSI Line Index")
    plt.ylabel("Angle [°]")
    plt.title("YAW Angle and Flight Direction (Azimuth) over HSI Line Index")
    plt.legend()
    plt.savefig(os.path.join(plot_output_dir, "plot_yaw_flight_direction.png"))
    plt.close()

def run_plotting(config_or_path: Union[Dict[str, Any], str]) -> bool:
    """
    Generate HSI data visualization plots.

    Args:
        config_or_path: Either a configuration dictionary or path to TOML file.

    Returns:
        True if plots were generated successfully, False otherwise.

    Raises:
        PipelineConfigError: If configuration is invalid or cannot be loaded
    """
    logger = get_logger(__name__)

    # --- Configuration Loading/Validation ---
    if isinstance(config_or_path, dict):
        config = config_or_path
        logger.info("Using provided configuration dictionary for plotting")
    elif isinstance(config_or_path, str):
        logger.info(f"Loading configuration from file: {config_or_path}")
        try:
            config = toml.load(config_or_path)
        except FileNotFoundError:
            raise PipelineConfigError(f"Configuration file not found: {config_or_path}")
        except Exception as e:
            raise PipelineConfigError(f"Error loading configuration file: {e}")
    else:
        raise PipelineConfigError(f"Invalid config parameter type: {type(config_or_path)}")

    # Get paths from configuration
    try:
        output_dir_from_config = config['paths']['output_directory']
        hsi_poses_filename = config['paths']['hsi_poses_csv']
        plot_output_dir = config['paths']['plot_output_directory']
    except KeyError as e:
        error_msg = f"Missing key in configuration: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    hsi_poses_input_path = os.path.join(output_dir_from_config, hsi_poses_filename)

    # Ensure plot output directory exists
    try:
        os.makedirs(plot_output_dir, exist_ok=True)
    except OSError as e:
        error_msg = f"Error creating directory {plot_output_dir}: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    # Read the CSV file into a pandas DataFrame
    try:
        data = pd.read_csv(hsi_poses_input_path)
    except FileNotFoundError:
        error_msg = f"HSI poses file not found: '{hsi_poses_input_path}'"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)
    except Exception as e:
        error_msg = f"Error reading '{hsi_poses_input_path}': {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

    # Create and save the plots
    try:
        plot_positions(data, plot_output_dir)
        logger.info(f"Saved {os.path.join(plot_output_dir, 'plot_positions_over_index.png')}")

        plot_trajectory_2d(data, plot_output_dir)
        logger.info(f"Saved {os.path.join(plot_output_dir, 'plot_trajectory_2d.png')}")

        plot_orientations(data, plot_output_dir)
        logger.info(f"Saved {os.path.join(plot_output_dir, 'plot_orientation_over_index.png')}")

        plot_interpolation_quality(data, plot_output_dir)
        logger.info(f"Saved {os.path.join(plot_output_dir, 'plot_interpolation_quality.png')}")

        plot_yaw_and_flight_direction(data, plot_output_dir)
        logger.info(f"Saved {os.path.join(plot_output_dir, 'plot_yaw_flight_direction.png')}")

        logger.info(f"All plots generated and saved successfully in '{plot_output_dir}'.")
        return True
    except Exception as e:
        error_msg = f"Error creating plots: {e}"
        logger.error(error_msg)
        raise PipelineConfigError(error_msg)

if __name__ == "__main__":
    DEFAULT_CONFIG_PATH = 'config.toml'
    logger = get_logger(__name__)
    logger.info(f"Creating HSI pose data plots with configuration: {DEFAULT_CONFIG_PATH}")

    try:
        success = run_plotting(config_or_path=DEFAULT_CONFIG_PATH)
        if success:
            logger.info("Plots created successfully.")
        else:
            logger.error("Error creating plots.")
    except PipelineConfigError as e:
        logger.error(f"Configuration error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error: {e}", exc_info=True)