<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">12%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-03 15:56 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0___init___py.html">src\hsi_pipeline\__init__.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html">src\hsi_pipeline\create_consolidated_webodm_poses.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_consolidated_webodm_poses_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>112</td>
                <td>105</td>
                <td>17</td>
                <td class="right" data-ratio="7 112">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html">src\hsi_pipeline\create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_create_georeferenced_rgb_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>192</td>
                <td>176</td>
                <td>18</td>
                <td class="right" data-ratio="16 192">8%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html">src\hsi_pipeline\georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_georeference_hsi_pixels_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>458</td>
                <td>438</td>
                <td>17</td>
                <td class="right" data-ratio="20 458">4%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html">src\hsi_pipeline\lever_arm_utils.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_lever_arm_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="44 56">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html">src\hsi_pipeline\logging_config.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_logging_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>14</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="7 14">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html">src\hsi_pipeline\main_pipeline.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_main_pipeline_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>83</td>
                <td>69</td>
                <td>6</td>
                <td class="right" data-ratio="14 83">17%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t9">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t9"><data value='PipelineError'>PipelineError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t14">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t14"><data value='PipelineConfigError'>PipelineConfigError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t19">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t19"><data value='HSIDataError'>HSIDataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t24">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t24"><data value='SynchronizationError'>SynchronizationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t29">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t29"><data value='GeoreferencingError'>GeoreferencingError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t34">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t34"><data value='DSMIntersectionError'>DSMIntersectionError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t39">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t39"><data value='InputDataError'>InputDataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t44">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t44"><data value='WebODMDataError'>WebODMDataError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t49">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t49"><data value='VectorizedProcessingError'>VectorizedProcessingError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t54">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html#t54"><data value='PoseTransformationError'>PoseTransformationError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html">src\hsi_pipeline\pipeline_exceptions.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_pipeline_exceptions_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8___init___py.html">src\hsi_pipeline\plotting\__init__.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html">src\hsi_pipeline\plotting\plot_hsi_data.py</a></td>
                <td class="name left"><a href="z_c979b7456c4d1cc8_plot_hsi_data_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>131</td>
                <td>117</td>
                <td>14</td>
                <td class="right" data-ratio="14 131">11%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html">src\hsi_pipeline\synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_synchronize_hsi_webodm_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>232</td>
                <td>217</td>
                <td>17</td>
                <td class="right" data-ratio="15 232">6%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html">src\hsi_pipeline\vectorized_georef.py</a></td>
                <td class="name left"><a href="z_1ab0a9e30448b8e0_vectorized_georef_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>128</td>
                <td>117</td>
                <td>0</td>
                <td class="right" data-ratio="11 128">9%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1437</td>
                <td>1258</td>
                <td>89</td>
                <td class="right" data-ratio="179 1437">12%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-03 15:56 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
