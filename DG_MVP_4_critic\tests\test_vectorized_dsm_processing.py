"""
Test vectorized DSM intersection processing in HSI georeferencing.

This module tests the LS8_1 enhancement that enables vectorized processing
for DSM intersection method, not just flat-plane methods.

Following TDD methodology from docs/TDD.md with Given-When-Then format.
"""

import pytest
import tempfile
import os
import numpy as np
from unittest.mock import patch, MagicMock
from pathlib import Path

from src.hsi_pipeline.georeference_hsi_pixels import run_georeferencing
from src.hsi_pipeline.pipeline_exceptions import GeoreferencingError, VectorizedProcessingError


class TestVectorizedDSMProcessing:
    """Test vectorized DSM intersection processing."""

    def test_vectorized_dsm_intersection_is_called(self):
        """
        Given a configuration with dsm_intersection z_ground_method
        When running georeferencing with the LS8_1 enhancement
        Then vectorized processing should be used for DSM intersection
        """
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': 'data/HSI/',
                'hsi_base_filename': 'test_hsi',
                'output_directory': '.',
                'hsi_poses_csv': 'test_poses.csv',
                'georeferenced_pixels_csv': 'test_pixels.csv',
                'sensor_model_file': 'test_sensor.txt',
                'dsm_file': 'test_dsm.tif'
            },
            'parameters': {
                'georeferencing': {
                    'boresight_roll_deg': 0.1,
                    'boresight_pitch_deg': -1.35,
                    'boresight_yaw_deg': 0,
                    'scale_vinkel_x': 1.0,
                    'offset_vinkel_x': 0.0,
                    'lever_arm_x_m': 0.0,
                    'lever_arm_y_m': 0.0,
                    'lever_arm_z_m': 0.0,
                    'z_ground_calculation_method': 'dsm_intersection',
                    'z_ground_offset_meters': 20.0,
                    'z_ground_fixed_value_meters': 100.0,
                    'ray_dsm_max_search_dist_m': 2000.0,
                    'ray_dsm_step_m': 5.0,
                    'ray_dsm_bisection_tolerance_m': 0.01
                }
            }
        }

        # Mock all external dependencies
        with patch('src.hsi_pipeline.georeference_hsi_pixels.spectral.open_image') as mock_spectral, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.pd.read_csv') as mock_read_csv, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.rasterio.open') as mock_rasterio, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.process_hsi_line_vectorized') as mock_vectorized, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.os.path.exists', return_value=True), \
             patch('src.hsi_pipeline.georeference_hsi_pixels.os.makedirs'):
            
            # Configure HSI header mock
            mock_img = MagicMock()
            mock_img.shape = (10, 20, 50)  # Small test size: 10 lines, 20 samples, 50 bands
            mock_img.metadata = {'lever arm x': '-462', 'lever arm y': '318', 'lever arm z': '85'}
            mock_spectral.return_value = mock_img
            
            # Configure sensor model mock
            sensor_data = []
            for i in range(20):  # 20 samples
                sensor_data.append(f"{i}\t{-0.1 + i*0.01}\t0.0")  # Simple linear sensor model
            mock_sensor_content = "\n".join(sensor_data)
            
            # Configure poses mock
            poses_data = []
            for i in range(10):  # 10 poses for 10 lines
                poses_data.append({
                    'hsi_line_index': i,
                    'timestamp': f'2025-01-01T10:00:{i:02d}',
                    'pos_x': 545750.0 + i * 0.1,
                    'pos_y': 5791370.0 + i * 0.1,
                    'pos_z': 120.0 + i * 0.1,
                    'quat_x': 0.0,
                    'quat_y': 0.0,
                    'quat_z': 0.0,
                    'quat_w': 1.0
                })
            mock_poses_df = MagicMock()
            mock_poses_df.__len__.return_value = 10
            mock_poses_df.iloc = lambda idx: poses_data[idx]
            mock_read_csv.return_value = mock_poses_df
            
            # Configure DSM mock
            mock_dsm = MagicMock()
            mock_dsm.read.return_value = np.ones((100, 100)) * 100.0  # Simple flat DSM at 100m
            mock_dsm.transform = MagicMock()
            mock_dsm.transform.a = 1.0
            mock_dsm.transform.b = 0.0
            mock_dsm.transform.c = 545700.0
            mock_dsm.transform.d = 0.0
            mock_dsm.transform.e = -1.0
            mock_dsm.transform.f = 5791400.0
            mock_dsm.bounds = MagicMock()
            mock_dsm.bounds.left = 545700.0
            mock_dsm.bounds.right = 545800.0
            mock_dsm.bounds.bottom = 5791300.0
            mock_dsm.bounds.top = 5791400.0
            mock_dsm.nodata = -9999.0
            mock_rasterio.return_value.__enter__.return_value = mock_dsm
            
            # Configure vectorized processing mock to return valid results
            def mock_vectorized_func(*args, **kwargs):
                line_index = kwargs.get('line_index', args[0] if args else 0)
                num_samples = kwargs.get('num_samples', args[2] if len(args) > 2 else 20)
                results = []
                for j in range(num_samples):
                    results.append({
                        'hsi_line_index': line_index,
                        'pixel_index': j,
                        'X_ground': 545750.0 + j * 0.1,
                        'Y_ground': 5791370.0 + j * 0.1,
                        'Z_ground': 100.0
                    })
                return results
            
            mock_vectorized.side_effect = mock_vectorized_func
            
            # Mock file operations
            with patch('builtins.open', mock_open_sensor_file(mock_sensor_content)):
                # Act
                try:
                    result = run_georeferencing(config)
                    
                    # Assert
                    assert result is True, "Georeferencing should complete successfully"
                    
                    # Verify vectorized processing was called for DSM intersection
                    assert mock_vectorized.called, "Vectorized processing should be called"
                    
                    # Check that DSM intersection method was used
                    call_args = mock_vectorized.call_args_list[0]
                    assert call_args[1]['z_ground_method'] == 'dsm_intersection', \
                        "Should use DSM intersection method in vectorized processing"
                    
                    # Verify DSM parameters were passed
                    assert 'dsm_interpolator' in call_args[1], "DSM interpolator should be passed"
                    assert 'dsm_bounds' in call_args[1], "DSM bounds should be passed"
                    assert 'ray_max_dist' in call_args[1], "Ray max distance should be passed"
                    
                except Exception as e:
                    pytest.fail(f"Georeferencing failed unexpectedly: {e}")

    def test_vectorized_fallback_to_individual_processing(self):
        """
        Given vectorized DSM processing fails
        When VectorizedProcessingError is raised
        Then should fallback to individual pixel processing
        """
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': 'data/HSI/',
                'hsi_base_filename': 'test_hsi',
                'output_directory': '.',
                'hsi_poses_csv': 'test_poses.csv',
                'georeferenced_pixels_csv': 'test_pixels.csv',
                'sensor_model_file': 'test_sensor.txt',
                'dsm_file': 'test_dsm.tif'
            },
            'parameters': {
                'georeferencing': {
                    'boresight_roll_deg': 0.1,
                    'boresight_pitch_deg': -1.35,
                    'boresight_yaw_deg': 0,
                    'scale_vinkel_x': 1.0,
                    'offset_vinkel_x': 0.0,
                    'lever_arm_x_m': 0.0,
                    'lever_arm_y_m': 0.0,
                    'lever_arm_z_m': 0.0,
                    'z_ground_calculation_method': 'dsm_intersection',
                    'z_ground_offset_meters': 20.0,
                    'z_ground_fixed_value_meters': 100.0,
                    'ray_dsm_max_search_dist_m': 2000.0,
                    'ray_dsm_step_m': 5.0,
                    'ray_dsm_bisection_tolerance_m': 0.01
                }
            }
        }

        # Mock external dependencies with vectorized processing failure
        with patch('src.hsi_pipeline.georeference_hsi_pixels.spectral.open_image') as mock_spectral, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.pd.read_csv') as mock_read_csv, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.rasterio.open') as mock_rasterio, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.process_hsi_line_vectorized') as mock_vectorized, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.calculate_ray_dsm_intersection') as mock_individual, \
             patch('src.hsi_pipeline.georeference_hsi_pixels.os.path.exists', return_value=True), \
             patch('src.hsi_pipeline.georeference_hsi_pixels.os.makedirs'):
            
            # Configure mocks similar to previous test
            mock_img = MagicMock()
            mock_img.shape = (2, 3, 50)  # Very small test: 2 lines, 3 samples
            mock_img.metadata = {'lever arm x': '-462', 'lever arm y': '318', 'lever arm z': '85'}
            mock_spectral.return_value = mock_img
            
            # Configure poses mock
            poses_data = [
                {'pos_x': 545750.0, 'pos_y': 5791370.0, 'pos_z': 120.0, 
                 'quat_x': 0.0, 'quat_y': 0.0, 'quat_z': 0.0, 'quat_w': 1.0},
                {'pos_x': 545750.1, 'pos_y': 5791370.1, 'pos_z': 120.1,
                 'quat_x': 0.0, 'quat_y': 0.0, 'quat_z': 0.0, 'quat_w': 1.0}
            ]
            mock_poses_df = MagicMock()
            mock_poses_df.__len__.return_value = 2
            mock_poses_df.iloc = lambda idx: poses_data[idx]
            mock_read_csv.return_value = mock_poses_df
            
            # Configure DSM mock
            mock_dsm = MagicMock()
            mock_dsm.read.return_value = np.ones((100, 100)) * 100.0
            mock_dsm.transform = MagicMock()
            mock_dsm.bounds = MagicMock()
            mock_dsm.nodata = -9999.0
            mock_rasterio.return_value.__enter__.return_value = mock_dsm
            
            # Configure vectorized processing to fail
            mock_vectorized.side_effect = VectorizedProcessingError("Simulated vectorized processing failure")
            
            # Configure individual processing to succeed
            mock_individual.return_value = (545750.0, 5791370.0, 100.0)
            
            # Mock sensor file
            sensor_content = "0\t-0.1\t0.0\n1\t-0.05\t0.0\n2\t0.0\t0.0"
            
            with patch('builtins.open', mock_open_sensor_file(sensor_content)):
                # Act & Assert
                try:
                    result = run_georeferencing(config)
                    
                    # Should complete successfully using fallback
                    assert result is True, "Should complete successfully with fallback processing"
                    
                    # Verify vectorized processing was attempted
                    assert mock_vectorized.called, "Vectorized processing should be attempted"
                    
                    # Verify individual processing was used as fallback
                    assert mock_individual.called, "Individual processing should be used as fallback"
                    
                except Exception as e:
                    pytest.fail(f"Georeferencing should not fail with proper fallback: {e}")


def mock_open_sensor_file(content):
    """Helper function to mock file opening for sensor model."""
    from unittest.mock import mock_open
    return mock_open(read_data=content)
