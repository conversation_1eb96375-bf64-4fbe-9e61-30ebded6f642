"""
Test module for lever arm source selection functionality.

This module tests the new lever_arm_source configuration option that allows
users to control whether lever arm values are taken from config, HDR, or
automatically selected based on non-zero values.

Test Coverage:
- Config source selection (always use config, even if zero)
- HDR source selection (always use HDR, even if zero)
- Auto source selection (original behavior)
- Invalid source handling
- Edge cases with None values
"""

import pytest
import numpy as np
from unittest.mock import patch, MagicMock

from src.hsi_pipeline.lever_arm_utils import determine_effective_lever_arm


class TestLeverArmSourceSelection:
    """Test class for lever arm source selection functionality."""

    def test_config_source_with_zero_values(self):
        """Test that config source uses config values even when they are zero."""
        # Given: Config has zero values, HDR has non-zero values
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using config source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="config"
        )
        
        # Then: Config values are used (even though zero)
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True

    def test_config_source_with_nonzero_values(self):
        """Test that config source uses config values when they are non-zero."""
        # Given: Config has non-zero values, HDR has different values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using config source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="config"
        )
        
        # Then: Config values are used
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True

    def test_config_source_with_none_config(self):
        """Test config source behavior when config is None."""
        # Given: Config is None, HDR has values
        config_lever_arm = None
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using config source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="config"
        )
        
        # Then: Falls back to zero with warning
        np.testing.assert_array_equal(effective_lever_arm, np.array([0.0, 0.0, 0.0]))
        assert config_used is True

    def test_hdr_source_with_zero_values(self):
        """Test that HDR source uses HDR values even when they are zero."""
        # Given: HDR has zero values, config has non-zero values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([0.0, 0.0, 0.0])
        
        # When: Using HDR source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="hdr"
        )
        
        # Then: HDR values are used (even though zero)
        np.testing.assert_array_equal(effective_lever_arm, hdr_lever_arm)
        assert config_used is False

    def test_hdr_source_with_nonzero_values(self):
        """Test that HDR source uses HDR values when they are non-zero."""
        # Given: HDR has non-zero values, config has different values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using HDR source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="hdr"
        )
        
        # Then: HDR values are used
        np.testing.assert_array_equal(effective_lever_arm, hdr_lever_arm)
        assert config_used is False

    def test_hdr_source_with_none_hdr(self):
        """Test HDR source behavior when HDR is None."""
        # Given: HDR is None, config has values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = None
        
        # When: Using HDR source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="hdr"
        )
        
        # Then: Falls back to zero with warning
        np.testing.assert_array_equal(effective_lever_arm, np.array([0.0, 0.0, 0.0]))
        assert config_used is False

    def test_auto_source_prefers_nonzero_config(self):
        """Test that auto source prefers non-zero config values."""
        # Given: Config has non-zero values, HDR has different non-zero values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using auto source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="auto"
        )
        
        # Then: Config values are preferred
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True

    def test_auto_source_falls_back_to_hdr(self):
        """Test that auto source falls back to HDR when config is zero."""
        # Given: Config has zero values, HDR has non-zero values
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using auto source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="auto"
        )
        
        # Then: HDR values are used
        np.testing.assert_array_equal(effective_lever_arm, hdr_lever_arm)
        assert config_used is False

    def test_auto_source_both_zero(self):
        """Test auto source behavior when both config and HDR are zero."""
        # Given: Both config and HDR have zero values
        config_lever_arm = np.array([0.0, 0.0, 0.0])
        hdr_lever_arm = np.array([0.0, 0.0, 0.0])
        
        # When: Using auto source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="auto"
        )
        
        # Then: Zero values are used with warning
        np.testing.assert_array_equal(effective_lever_arm, np.array([0.0, 0.0, 0.0]))
        assert config_used is False

    def test_invalid_source_falls_back_to_auto(self):
        """Test that invalid source values fall back to auto mode."""
        # Given: Invalid source, config has non-zero values
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using invalid source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="invalid"
        )
        
        # Then: Falls back to auto behavior (prefers config)
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True

    def test_default_source_is_auto(self):
        """Test that default source behavior is auto."""
        # Given: Config has non-zero values, no source specified
        config_lever_arm = np.array([0.5, 1.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Not specifying source (should default to auto)
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm
        )
        
        # Then: Auto behavior (prefers config)
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True

    def test_small_nonzero_values_are_detected(self):
        """Test that small but non-zero values are properly detected."""
        # Given: Config has very small non-zero values
        config_lever_arm = np.array([1e-10, 1e-10, 1e-10])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using auto source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="auto"
        )
        
        # Then: Small values are considered zero, HDR is used
        np.testing.assert_array_equal(effective_lever_arm, hdr_lever_arm)
        assert config_used is False

    def test_mixed_zero_nonzero_config_values(self):
        """Test behavior with mixed zero and non-zero config values."""
        # Given: Config has mixed zero and non-zero values
        config_lever_arm = np.array([0.5, 0.0, 1.5])
        hdr_lever_arm = np.array([1.0, 2.0, 3.0])
        
        # When: Using auto source
        effective_lever_arm, config_used = determine_effective_lever_arm(
            hdr_lever_arm, config_lever_arm, lever_arm_source="auto"
        )
        
        # Then: Config is considered non-zero (has at least one non-zero component)
        np.testing.assert_array_equal(effective_lever_arm, config_lever_arm)
        assert config_used is True
