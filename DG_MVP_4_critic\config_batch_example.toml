# HSI Georeferencing Pipeline - Batch Processing Configuration Example
# This configuration demonstrates how to process multiple HSI datasets in a single pipeline run
# Each dataset will generate its own individual GeoTIFF file with dataset-specific naming

# Enable batch processing mode
batch_mode = true

# Continue processing other datasets if one fails
continue_on_error = true

[batch_settings]
# Base directory for all batch outputs
output_base_directory = "batch_outputs"

# Create individual subdirectories for each dataset
create_dataset_subdirs = true

# Add timestamps to output files
timestamp_outputs = true

# Global paths (shared across all datasets)
[paths]
# These will be overridden by dataset-specific paths
output_directory = "."  # Will be set automatically for each dataset

# Global parameters that apply to all datasets unless overridden
[parameters.webodm_consolidation]
haip_timestamp_key = "rgb"

[parameters.georeferencing]
# Global georeferencing parameters
boresight_roll_deg = 0
boresight_pitch_deg = 0
boresight_yaw_deg = 0
scale_vinkel_x = 1
offset_vinkel_x = 0

# Lever arm configuration
lever_arm_x_m = 0.0
lever_arm_y_m = 0.0
lever_arm_z_m = 0.0
lever_arm_source = "config"

# Ground elevation calculation
z_ground_calculation_method = "dsm_intersection"
z_ground_offset_meters = 20.0
z_ground_fixed_value_meters = 100.0

# Ray-DSM intersection parameters
ray_dsm_max_search_dist_m = 2000.0
ray_dsm_step_m = 5.0
ray_dsm_bisection_tolerance_m = 0.01

[parameters.rgb_geotiff_creation]
# RGB band mapping (shared across all datasets)
target_wavelength_R_nm = 800.0
target_wavelength_G_nm = 700.0
target_wavelength_B_nm = 550.0

# Output resolution and projection
target_resolution_meters = 0.1
output_epsg_code = 32632  # UTM Zone 32N, WGS84

# Normalization method
normalization_method = "min_max"

[parameters.plotting]
# Plotting parameters (shared across all datasets)

[parameters.pipeline_options]
processing_mode = "vectorized"

# Dataset-specific configurations
# Each dataset will generate: georeferenced_rgb_{dataset_name}_{timestamp}.tif

[[datasets]]
name = "flight_001"
hsi_data_directory = "data/HSI/flight_001/"
hsi_base_filename = "2025-05-15_08-28-48_cont"
webodm_data_directory = "data/WebODM/flight_001/"
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# Optional: Dataset-specific parameter overrides
[datasets.parameters.georeferencing]
boresight_yaw_deg = 1.5  # Override for this specific dataset

[[datasets]]
name = "flight_002"
hsi_data_directory = "data/HSI/flight_002/"
hsi_base_filename = "2025-05-15_09-15-30_cont"
webodm_data_directory = "data/WebODM/flight_002/"
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# Optional: Different RGB mapping for this dataset
[datasets.parameters.rgb_geotiff_creation]
target_wavelength_R_nm = 850.0
target_wavelength_G_nm = 750.0
target_wavelength_B_nm = 600.0

[[datasets]]
name = "flight_003"
hsi_data_directory = "data/HSI/flight_003/"
hsi_base_filename = "2025-05-15_10-45-12_cont"
webodm_data_directory = "data/WebODM/flight_003/"
sensor_model_file = "Sensormodel_HAIP_BlackBirdV2_No6_20m.txt"

# Optional: Different resolution for this dataset
[datasets.parameters.rgb_geotiff_creation]
target_resolution_meters = 0.05  # Higher resolution for this dataset

# Expected Output Structure:
# batch_outputs/
# ├── batch_20250603_160349/
# │   ├── batch_summary_20250603_160349.json
# │   ├── batch_log_20250603_160349.log
# │   ├── flight_001/
# │   │   ├── webodm_poses_consolidated.csv
# │   │   ├── hsi_poses.csv
# │   │   ├── georeferenced_pixels.csv
# │   │   ├── georeferenced_rgb_flight_001_20250603_160349.tif  ← Individual GeoTIFF
# │   │   └── plots/
# │   │       ├── plot_positions_over_index.png
# │   │       └── ...
# │   ├── flight_002/
# │   │   ├── webodm_poses_consolidated.csv
# │   │   ├── hsi_poses.csv
# │   │   ├── georeferenced_pixels.csv
# │   │   ├── georeferenced_rgb_flight_002_20250603_160349.tif  ← Individual GeoTIFF
# │   │   └── plots/
# │   └── flight_003/
# │       ├── webodm_poses_consolidated.csv
# │       ├── hsi_poses.csv
# │       ├── georeferenced_pixels.csv
# │       ├── georeferenced_rgb_flight_003_20250603_160349.tif  ← Individual GeoTIFF
# │       └── plots/
